#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UFN协议消息解析器
"""

import struct
import logging
from typing import Dict, Optional
from .constants import FunctionCode

logger = logging.getLogger(__name__)


class MessageParser:
    """UFN协议消息解析器"""
    
    @staticmethod
    def calculate_checksum(data: bytes) -> int:
        """计算检验和（前面所有数据和）"""
        return sum(data) & 0xFFFFFFFF
    
    @staticmethod
    def parse_get_version_request(data: bytes) -> Optional[Dict]:
        """解析获取版本信息请求"""
        if len(data) < 50:  # 0x32 = 50
            return None
        
        try:
            # 解析报文
            length = struct.unpack('>H', data[0:2])[0]  # 大端
            func_code = struct.unpack('>H', data[2:4])[0]  # 大端
            
            if func_code != FunctionCode.GET_VERSION_REQ.value:
                return None
            
            program_id = data[4:12].rstrip(b'\x00')  # 保持为字节，移除尾部的空字节
            area = data[12]
            option = data[13]
            mark1 = data[14]
            mark2 = data[15]
            chip_id = data[16:28].hex()
            device_version = struct.unpack('>H', data[28:30])[0] #大端
            device_number = data[30:46].decode('ascii', errors='ignore').strip('\x00') # device id
            checksum = struct.unpack('<I', data[46:50])[0]
            
            # 验证检验和
            calc_checksum = MessageParser.calculate_checksum(data[0:46])
            if calc_checksum != checksum:
                logger.warning(f"Checksum mismatch: calc={calc_checksum}, recv={checksum}")
                return None
            
            return {
                'program_id': program_id,
                'area': area,
                'option': option,
                'mark1': mark1,
                'mark2': mark2,
                'chip_id': chip_id,
                'device_version': device_version,
                'device_number': device_number
            }
        except Exception as e:
            logger.error(f"Parse get version request error: {e}")
            return None
    
    @staticmethod
    def parse_get_program_info_request(data: bytes) -> bool:
        """解析获取程序信息请求"""
        if len(data) < 8:
            return False
        
        try:
            length = struct.unpack('>H', data[0:2])[0]
            func_code = struct.unpack('>H', data[2:4])[0]
            checksum = struct.unpack('<I', data[4:8])[0]
            
            # 验证检验和
            calc_checksum = MessageParser.calculate_checksum(data[0:4])
            return func_code == FunctionCode.GET_PROGRAM_INFO_REQ.value and calc_checksum == checksum
        except Exception as e:
            logger.error(f"Parse get program info request error: {e}")
            return False
    
    @staticmethod
    def parse_download_request(data: bytes) -> Optional[Dict]:
        """解析下载程序数据请求"""
        if len(data) < 10:
            return None
        
        try:
            length = struct.unpack('>H', data[0:2])[0]
            func_code = struct.unpack('>H', data[2:4])[0]
            data_index = struct.unpack('>I', data[4:8])[0]
            request_length = struct.unpack('>H', data[8:10])[0]
            checksum = struct.unpack('<I', data[10:14])[0]
            
            # 验证检验和
            calc_checksum = MessageParser.calculate_checksum(data[0:10])
            if func_code != FunctionCode.DOWNLOAD_DATA_REQ.value or calc_checksum != checksum:
                return None
            
            return {
                'data_index': data_index,
                'request_length': request_length
            }
        except Exception as e:
            logger.error(f"Parse download request error: {e}")
            return None
    
    @staticmethod
    def parse_startup_info(data: bytes) -> Optional[Dict]:
        """解析启动信息"""
        if len(data) < 36:
            return None
        
        try:
            length = struct.unpack('<H', data[0:2])[0]
            func_code = struct.unpack('>H', data[2:4])[0]
            
            if func_code != FunctionCode.SEND_STARTUP_REQ.value:
                return None
            
            program_id = data[4:12].hex()
            ccu_version = struct.unpack('<H', data[12:14])[0]
            device_number = data[14:30].decode('ascii', errors='ignore').strip('\x00')
            bms_version = struct.unpack('<H', data[30:32])[0]
            checksum = struct.unpack('<I', data[32:36])[0]
            
            # 验证检验和
            calc_checksum = MessageParser.calculate_checksum(data[0:32])
            if calc_checksum != checksum:
                return None
            
            return {
                'program_id': program_id,
                'ccu_version': ccu_version,
                'device_number': device_number,
                'bms_version': bms_version,
                'raw_data': data
            }
        except Exception as e:
            logger.error(f"Parse startup info error: {e}")
            return None