#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端连接处理器
"""

import socket
import struct
import logging
from typing import Dict, Any, Optional
from .constants import FunctionCode, OTASID
from .message_parser import MessageParser
from .message_builder import MessageBuilder
from .program_manager import ProgramManager
from .grpc_client import GRPCClient, DeviceUpgradeStatus

logger = logging.getLogger(__name__)


class ClientHandler:
    """客户端连接处理器"""
    
    def __init__(self, program_manager: ProgramManager, grpc_client: GRPCClient, timeout: int = 10):
        self.program_manager = program_manager
        self.grpc_client = grpc_client
        self.timeout = timeout
        # 跟踪设备下载状态，key为device_number，value为是否已发送升级开始状态
        self.device_download_status = {}
    
    def handle_client(self, client_socket: socket.socket, client_address):
        """处理客户端连接"""
        logger.info(f"New client connected: {client_address}")
        client_socket.settimeout(self.timeout)
        
        upgrade_context = None
        
        try:
            while True:
                try:
                    # 读取报文长度
                    onetime_data = client_socket.recv(64)
                    length_data = onetime_data[:2]
                    # length_data = client_socket.recv(2)
                    if not length_data:
                        break
                    
                    length = struct.unpack('>H', length_data)[0] # 大端

                    # 检查长度是否有效
                    if length < 2:
                        logger.error(f"Invalid message length: {length}")
                        break

                    full_data = onetime_data[:length]
                    
                    # 解析功能代码
                    func_code = struct.unpack('>H', full_data[2:4])[0] # 大端
                    
                    if func_code == FunctionCode.GET_VERSION_REQ.value:
                        logger.info(f"<- 收到设备{client_address} 获取版本信息请求 {func_code}")
                        upgrade_context = self._handle_get_version(client_socket, full_data) 
                    
                    elif func_code == FunctionCode.GET_PROGRAM_INFO_REQ.value:
                        logger.info(f"<- 收到设备{client_address} 获取程序信息请求 {func_code}")
                        self._handle_get_program_info(client_socket, full_data, upgrade_context)
                    
                    elif func_code == FunctionCode.DOWNLOAD_DATA_REQ.value:
                        logger.info(f"<- 收到设备{client_address} 下载程序数据请求 {func_code}")
                        self._handle_download_data(client_socket, full_data, upgrade_context)
                    
                    elif func_code == FunctionCode.SEND_STARTUP_REQ.value:
                        logger.info(f"<- 收到设备{client_address} 发送启动信息请求 {func_code}")
                        self._handle_startup_info(client_socket, full_data)
                
                except socket.timeout:
                    logger.warning(f"Client {client_address} timeout")
                    break
                except Exception as e:
                    logger.error(f"Handle client {client_address} error: {e}")
                    break
        
        finally:
            client_socket.close()
            logger.info(f"Client {client_address} disconnected")
    
    def _handle_get_version(self, client_socket: socket.socket, data: bytes) -> Dict[str, Any]: #tuple:
        """处理获取版本信息请求"""
        request_data = MessageParser.parse_get_version_request(data)
        if not request_data:
            return None, None
        
        program_id = request_data['program_id']
        device_number = request_data['device_number']
        device_version = request_data['device_version']
        
        # 通过 gRPC 查询升级包信息
        upgrade_info = self.grpc_client.query_upgrade_info(device_number, program_id)
        
        if upgrade_info and upgrade_info['program_id'] == program_id:
            platform_version = int(upgrade_info['platform_version'])
            
            # 判断是否需要升级
            if platform_version > device_version:
                # 构建响应数据
                upgrade_type = OTASID.get_grpc_value(program_id)
                response_data = {
                    'program_id': program_id,  # 添加 program_id 字段
                    'device_number': device_number,  # 添加 device_number 字段
                    'platform_version': platform_version,
                    'upgrade_type': upgrade_type if upgrade_type > 0 else 0,
                    'file_url': upgrade_info['file_url'],  # 保留用于兼容性
                    'file_data': upgrade_info['file_data'],  # 直接包含文件字节数组
                    'upgrade_available': True,
                    'area': request_data['area'],
                    'option': request_data['option'],
                    'mark1': request_data['mark1'],
                    'mark2': request_data['mark2']
                }
                
                response = MessageBuilder.create_get_version_response(response_data)
                client_socket.send(response)
                logger.info(f"-> 回复：{response}")
                logger.info(f"Sent version info for program {program_id}, version:{platform_version}, device No.: {device_number}")
                
                return response_data
            else:
                logger.info(f"No upgrade needed: device version {device_version} >= platform version {platform_version}")
        else:
            logger.warning(f"No upgrade available or program ID mismatch for {program_id}")
        
        # 没有升级包或不需要升级
        upgrade_type = OTASID.get_grpc_value(program_id)
        response_data = {
            'platform_version': device_version,
            'upgrade_type': upgrade_type if upgrade_type > 0 else 0,
            'upgrade_available': False,
            'area': request_data['area'],
            'option': request_data['option'],
            'mark1': request_data['mark1'],
            'mark2': request_data['mark2']
        }
        
        response = MessageBuilder.create_get_version_response(response_data)
        logger.info(f"-> 回复：{response}")
        client_socket.send(response)
        
        return request_data
    
    def _handle_get_program_info(self, client_socket: socket.socket, data: bytes,
                                upgrade_context: dict):
        """处理获取程序信息请求 (8403)"""
        if not MessageParser.parse_get_program_info_request(data) or not upgrade_context:
            return
        
        program_id = upgrade_context['program_id']
        version = upgrade_context['platform_version']
        
        # 加载升级程序，使用直接获取的文件数据
        success = self.program_manager.load_program_from_grpc(
            program_id=program_id,
            file_data=upgrade_context['file_data'],
            version=version,
            area=upgrade_context['area'],
            option=upgrade_context['option'],
            mark1=upgrade_context['mark1'],
            mark2=upgrade_context['mark2'],
            file_url=upgrade_context.get('file_url')  # 可选，仅用于日志
        )
        
        if success:
            response = MessageBuilder.create_get_program_info_response(
                program_id, 
                version,
                self.program_manager
            )
            if response:
                logger.info(f"-> 回复：{response}")
                client_socket.send(response)
                logger.info(f"Sent program info for {program_id} version {version}, loaded via gRPC")
        else:
            logger.error(f"Failed to load program via gRPC for {program_id} version {version}")
    
    def _handle_download_data(self, client_socket: socket.socket, data: bytes, upgrade_context: dict):
        """处理下载程序数据请求"""
        request_data = MessageParser.parse_download_request(data)
        if not request_data or not upgrade_context:
            return

        program_id = upgrade_context['program_id']
        version = upgrade_context['platform_version']
        device_number = upgrade_context.get('device_number')
        data_index = request_data['data_index']
        request_length = request_data['request_length']

        # 检查是否是第一次下载请求（data_index为0）
        if device_number and data_index == 0:
            if device_number not in self.device_download_status:
                # 第一次请求，发送升级开始状态
                if self.grpc_client:
                    success = self.grpc_client.update_device_status(
                        device_number,
                        DeviceUpgradeStatus.UPGRADE_STARTED,
                        f"开始下载程序 {program_id} 版本 {version}"
                    )
                    if success:
                        self.device_download_status[device_number] = True
                        logger.info(f"Sent upgrade started status for device {device_number}")
                    else:
                        logger.error(f"Failed to send upgrade started status for device {device_number}")

        response = MessageBuilder.create_download_response(
            self.program_manager,
            program_id,
            version,
            data_index,
            request_length
        )

        if response:
            client_socket.send(response)
            logger.info(f"-> 回复：{response}")
            logger.info(f"Sent data chunk at index {data_index} for {program_id} version {version}")

            # 检查是否是最后一块数据（下发数据长度 < 申请数据长度）
            # 从响应中解析实际下发的数据长度
            actual_length = struct.unpack('>H', response[8:10])[0]
            if actual_length < request_length:
                # 数据下发结束，发送升级结束状态
                if device_number and self.grpc_client:
                    success = self.grpc_client.update_device_status(
                        device_number,
                        DeviceUpgradeStatus.UPGRADE_ENDED,
                        f"程序 {program_id} 版本 {version} 下载完成"
                    )
                    if success:
                        # 清除设备下载状态
                        self.device_download_status.pop(device_number, None)
                        logger.info(f"Sent full upgrade data for device {device_number}")
                    else:
                        logger.error(f"Failed to send full upgrade completed status for device {device_number}")
    
    def _handle_startup_info(self, client_socket: socket.socket, data: bytes):
        """处理启动信息"""
        startup_data = MessageParser.parse_startup_info(data)
        if startup_data:
            # 原封不动返回
            logger.info(f"-> 回复：{startup_data['raw_data']}")
            client_socket.send(startup_data['raw_data'])
            success = self.grpc_client.update_device_status(
                startup_data['device_number'],
                DeviceUpgradeStatus.UPGRADE_COMPLETED,
                f"设备 {startup_data['device_number']} 升级完成"
            )
            if success:
                # 清除设备下载状态
                self.device_download_status.pop(startup_data['device_number'], None)
                logger.info(f"设备 {startup_data['device_number']} 升级完成")
            else:
                logger.error(f"设备 {startup_data['device_number']} 升级未完成")
            logger.info(f"Echoed startup info from {startup_data['device_number']}")
