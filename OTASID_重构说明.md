# OTASID 枚举类重构说明

## 重构目标

重新构写OTASID枚举类，实现以下需求：
1. 当UFN收到数据并解析到`program_id`为`b'NTB2GV31'`时代表需要CCU升级
2. 当UFN收到数据并解析到`program_id`为`b'RNBMSV20'`时代表需要BMS升级
3. 为了方便与gRPC通信，CCU用1表示，BMS用2表示
4. 使用更简单的方法实现这个枚举类型

## 重构前的问题

原始实现存在以下问题：
1. 使用类级别的字典作为IntEnum的属性，导致类型错误
2. 方法名称不够清晰，容易混淆
3. 代码复杂度较高，维护困难

## 重构后的实现

### 新的OTASID类结构

```python
class OTASID(IntEnum):
    """OTA 升级类型：1 - CCU, 2 - BMS"""
    CCU = 1
    BMS = 2

    @property
    def byte_value(self) -> bytes:
        """获取字节表示"""
        if self == OTASID.CCU:
            return b'NTB2GV31'
        elif self == OTASID.BMS:
            return b'RNBMSV20'
        else:
            return b''

    @classmethod
    def from_byte_value(cls, byte_val: bytes):
        """根据字节值返回枚举成员"""
        if isinstance(byte_val, str):
            byte_val = byte_val.encode('ascii')
        
        if byte_val == b'NTB2GV31':
            return cls.CCU
        elif byte_val == b'RNBMSV20':
            return cls.BMS
        else:
            return None

    @classmethod
    def get_grpc_value(cls, byte_val: bytes) -> int:
        """字节值转grpc program_id (int) - 用于grpc通信"""
        if isinstance(byte_val, str):
            byte_val = byte_val.encode('ascii')
            
        if byte_val == b'NTB2GV31':
            return 1  # CCU
        elif byte_val == b'RNBMSV20':
            return 2  # BMS
        else:
            return 0  # UNKNOWN

    @classmethod
    def get_byte_by_grpc_value(cls, grpc_value: int) -> bytes:
        """grpc program_id (int) 转字节值"""
        if grpc_value == 1:
            return b'NTB2GV31'  # CCU
        elif grpc_value == 2:
            return b'RNBMSV20'  # BMS
        else:
            return b''
```

### 主要改进

1. **简化实现**：移除了类级别的字典，使用简单的if-elif条件判断
2. **清晰的方法命名**：
   - `get_grpc_value()`: 专门用于获取gRPC通信的数值
   - `get_byte_by_grpc_value()`: 专门用于从gRPC值转换回字节值
3. **类型安全**：避免了IntEnum与字典混合使用的问题
4. **易于维护**：代码结构清晰，逻辑简单

## 更新的文件

### 1. ufn_protocol/constants.py
- 重构了OTASID类的完整实现

### 2. ufn_protocol/grpc_client.py
- 更新了`query_upgrade_info`方法，使用新的`get_grpc_value()`方法
- 更新了返回结果构建，使用新的`get_byte_by_grpc_value()`方法

### 3. ufn_protocol/client_handler.py
- 更新了`handle_get_version_request`方法中的两处调用
- 使用新的`get_grpc_value()`方法替换旧的`get_value_by_byte()`

### 4. ufn_protocol/message_parser.py
- 修改了`parse_get_version_request`方法，保持program_id为字节类型而不是字符串

## 测试验证

### 测试用例

1. **CCU升级测试**：
   - 输入：`b'NTB2GV31'`
   - 枚举成员：`OTASID.CCU`
   - gRPC值：`1`

2. **BMS升级测试**：
   - 输入：`b'RNBMSV20'`
   - 枚举成员：`OTASID.BMS`
   - gRPC值：`2`

3. **未知值测试**：
   - 输入：`b'UNKNOWN1'`
   - 枚举成员：`None`
   - gRPC值：`0`

### 测试结果

所有测试用例均通过，验证了：
- 字节值到枚举成员的转换正确
- 字节值到gRPC值的转换正确
- gRPC值到字节值的反向转换正确
- 未知值的处理正确

## 兼容性

重构后的实现完全兼容现有的gRPC通信协议：
- CCU对应gRPC值1
- BMS对应gRPC值2
- 未知类型对应gRPC值0

## 总结

重构成功实现了以下目标：
1. ✅ 简化了OTASID枚举类的实现
2. ✅ 提供了清晰的gRPC通信接口
3. ✅ 保持了与现有系统的兼容性
4. ✅ 提高了代码的可维护性和可读性
5. ✅ 通过了完整的测试验证

新的实现更加简洁、高效，并且易于理解和维护。
