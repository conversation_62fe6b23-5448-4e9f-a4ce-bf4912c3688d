2025-07-25 16:44:50,358 - INFO - <PERSON>ce Message Test Script
2025-07-25 16:44:50,358 - INFO - ==================================================
2025-07-25 16:44:50,358 - INFO - Starting OTA flow test...
2025-07-25 16:44:50,359 - INFO - Connected to server localhost:8080
2025-07-25 16:44:50,359 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 16:44:50,359 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 16:44:50,359 - INFO - Message length: 64 bytes
2025-07-25 16:44:50,363 - INFO - Received response: 00188302271001a4646464640000000309
2025-07-25 16:44:50,363 - INFO - Response length: 17 bytes
2025-07-25 16:44:50,363 - INFO - Response length: 24
2025-07-25 16:44:50,363 - INFO - Function code: 0x8302
2025-07-25 16:44:50,363 - INFO - Platform version: 10000
2025-07-25 16:44:50,363 - INFO - Upgrade type: 1
2025-07-25 16:44:50,363 - INFO - Upgrade control: 0xA4
2025-07-25 16:44:50,363 - INFO - Area: 100, Option: 100
2025-07-25 16:44:50,363 - INFO - Upgrade available: False
2025-07-25 16:44:50,363 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 16:44:50,363 - INFO - Disconnected from server
2025-07-25 16:44:50,363 - ERROR - ✗ Some tests failed!
2025-07-25 16:46:22,598 - INFO - Device Message Test Script
2025-07-25 16:46:22,598 - INFO - ==================================================
2025-07-25 16:46:22,598 - INFO - Starting OTA flow test...
2025-07-25 16:46:22,599 - INFO - Connected to server localhost:8080
2025-07-25 16:46:22,599 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 16:46:22,599 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 16:46:22,599 - INFO - Message length: 64 bytes
2025-07-25 16:46:22,601 - INFO - Received response: 00188302271001a4646464640000000309
2025-07-25 16:46:22,601 - INFO - Response length: 17 bytes
2025-07-25 16:46:22,601 - INFO - Response length: 24
2025-07-25 16:46:22,601 - INFO - Function code: 0x8302
2025-07-25 16:46:22,601 - INFO - Platform version: 10000
2025-07-25 16:46:22,601 - INFO - Upgrade type: 1
2025-07-25 16:46:22,601 - INFO - Upgrade control: 0xA4
2025-07-25 16:46:22,601 - INFO - Area: 100, Option: 100
2025-07-25 16:46:22,601 - INFO - Upgrade available: False
2025-07-25 16:46:22,601 - WARNING - No upgrade available or upgrade forbidden
2025-07-25 16:46:22,601 - INFO - Disconnected from server
2025-07-25 16:46:22,601 - ERROR - ✗ Some tests failed!
2025-07-25 16:47:08,874 - INFO - Device Message Test Script
2025-07-25 16:47:08,874 - INFO - ==================================================
2025-07-25 16:47:08,874 - INFO - Starting OTA flow test...
2025-07-25 16:47:08,875 - INFO - Connected to server localhost:8080
2025-07-25 16:47:08,875 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 16:47:08,875 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 16:47:08,875 - INFO - Message length: 64 bytes
2025-07-25 16:47:08,947 - INFO - Received response: 0018830228a001a1646464640000000397
2025-07-25 16:47:08,948 - INFO - Response length: 17 bytes
2025-07-25 16:47:08,948 - INFO - Response length: 24
2025-07-25 16:47:08,948 - INFO - Function code: 0x8302
2025-07-25 16:47:08,948 - INFO - Platform version: 10400
2025-07-25 16:47:08,948 - INFO - Upgrade type: 1
2025-07-25 16:47:08,948 - INFO - Upgrade control: 0xA1
2025-07-25 16:47:08,948 - INFO - Area: 100, Option: 100
2025-07-25 16:47:08,948 - INFO - Upgrade available: True
2025-07-25 16:47:08,948 - INFO - Version check passed, upgrade available
2025-07-25 16:47:08,948 - INFO - 
=== Step 2: Send Get Program Info Request (8403) ===
2025-07-25 16:47:08,948 - INFO - Sending message: 00 08 84 03 00 00 00 8f 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 16:47:08,948 - INFO - Message length: 64 bytes
2025-07-25 16:47:09,015 - INFO - Received response: 48008404a028190719102f09ffffffffffffffff060903006cfe4f014e5442324756333164646464ffffffffffffffffffffffffffffffffffffffffffffffffffffffff00002b68
2025-07-25 16:47:09,016 - INFO - Response length: 72 bytes
2025-07-25 16:47:09,016 - INFO - Response length: 18432
2025-07-25 16:47:09,016 - INFO - Function code: 0x8404
2025-07-25 16:47:09,016 - INFO - Received program info response
2025-07-25 16:47:09,016 - INFO - Program version: 10400
2025-07-25 16:47:09,016 - INFO - Date: 2025-07-25 16:47:09
2025-07-25 16:47:09,016 - INFO - Program length: 198918 bytes
2025-07-25 16:47:09,016 - INFO - Program checksum: 0x014FFE6C
2025-07-25 16:47:09,016 - INFO - Program ID: NTB2GV31
2025-07-25 16:47:09,016 - INFO - Program info received successfully
2025-07-25 16:47:09,016 - INFO - 
=== Step 3: Simulate Download Data Request (Optional) ===
2025-07-25 16:47:09,016 - INFO - Download simulation skipped for this test
2025-07-25 16:47:09,016 - INFO - 
=== OTA Flow Test Completed Successfully ===
2025-07-25 16:47:09,016 - INFO - Disconnected from server
2025-07-25 16:47:09,016 - INFO - ✓ All tests passed!
2025-07-25 17:22:04,454 - INFO - Device Message Test Script
2025-07-25 17:22:04,454 - INFO - ==================================================
2025-07-25 17:22:04,454 - INFO - Starting OTA flow test...
2025-07-25 17:22:04,455 - INFO - Connected to server localhost:8080
2025-07-25 17:22:04,455 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-25 17:22:04,455 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 00 00 13 b0 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 17:22:04,455 - INFO - Message length: 64 bytes
2025-07-25 17:22:04,528 - INFO - Received response: 0018830228a001a1646464640000000397
2025-07-25 17:22:04,528 - INFO - Response length: 17 bytes
2025-07-25 17:22:04,528 - INFO - Response length: 24
2025-07-25 17:22:04,528 - INFO - Function code: 0x8302
2025-07-25 17:22:04,528 - INFO - Platform version: 10400
2025-07-25 17:22:04,528 - INFO - Upgrade type: 1
2025-07-25 17:22:04,528 - INFO - Upgrade control: 0xA1
2025-07-25 17:22:04,528 - INFO - Area: 100, Option: 100
2025-07-25 17:22:04,528 - INFO - Upgrade available: True
2025-07-25 17:22:04,528 - INFO - Version check passed, upgrade available
2025-07-25 17:22:04,529 - INFO - 
=== Step 2: Send Get Program Info Request (8403) ===
2025-07-25 17:22:04,529 - INFO - Sending message: 00 08 84 03 00 00 00 8f 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 17:22:04,529 - INFO - Message length: 64 bytes
2025-07-25 17:22:04,586 - INFO - Received response: 48008404a028190719111604ffffffffffffffff060903006cfe4f014e5442324756333164646464ffffffffffffffffffffffffffffffffffffffffffffffffffffffff00002b4b
2025-07-25 17:22:04,586 - INFO - Response length: 72 bytes
2025-07-25 17:22:04,586 - INFO - Response length: 18432
2025-07-25 17:22:04,586 - INFO - Function code: 0x8404
2025-07-25 17:22:04,586 - INFO - Received program info response
2025-07-25 17:22:04,586 - INFO - Program version: 10400
2025-07-25 17:22:04,587 - INFO - Date: 2025-07-25 17:22:04
2025-07-25 17:22:04,587 - INFO - Program length: 198918 bytes
2025-07-25 17:22:04,587 - INFO - Program checksum: 0x014FFE6C
2025-07-25 17:22:04,587 - INFO - Program ID: NTB2GV31
2025-07-25 17:22:04,587 - INFO - Program info received successfully
2025-07-25 17:22:04,587 - INFO - 
=== Step 3: Simulate Download Data Request (Optional) ===
2025-07-25 17:22:04,587 - INFO - Sending message: 00 0e 91 01 00 00 00 00 04 00 a8 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-25 17:22:04,587 - INFO - Message length: 64 bytes
2025-07-25 17:22:14,588 - INFO - Received response: 
2025-07-25 17:22:14,589 - INFO - Response length: 0 bytes
2025-07-25 17:22:14,589 - ERROR - Failed to get download data response
2025-07-25 17:22:14,589 - INFO - Disconnected from server
2025-07-25 17:22:14,589 - ERROR - ✗ Some tests failed!
2025-07-28 10:46:54,555 - INFO - Device Message Test Script
2025-07-28 10:46:54,555 - INFO - ==================================================
2025-07-28 10:46:54,555 - INFO - Starting OTA flow test...
2025-07-28 10:46:54,557 - INFO - Connected to server localhost:8080
2025-07-28 10:46:54,557 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-28 10:46:54,557 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 10:46:54,557 - INFO - Message length: 64 bytes
2025-07-28 10:46:54,628 - INFO - Received response: 0018830228a001a1646464640000000397
2025-07-28 10:46:54,628 - INFO - Response length: 17 bytes
2025-07-28 10:46:54,628 - INFO - Response length: 24
2025-07-28 10:46:54,628 - INFO - Function code: 0x8302
2025-07-28 10:46:54,628 - INFO - Platform version: 10400
2025-07-28 10:46:54,628 - INFO - Upgrade type: 1
2025-07-28 10:46:54,628 - INFO - Upgrade control: 0xA1
2025-07-28 10:46:54,628 - INFO - Area: 100, Option: 100
2025-07-28 10:46:54,628 - INFO - Upgrade available: True
2025-07-28 10:46:54,628 - INFO - Version check passed, upgrade available
2025-07-28 10:46:54,628 - INFO - 
=== Step 2: Send Get Program Info Request (8403) ===
2025-07-28 10:46:54,628 - INFO - Sending message: 00 08 84 03 8f 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 10:46:54,628 - INFO - Message length: 64 bytes
2025-07-28 10:46:54,696 - INFO - Received response: 48008404a02819071c0a2e36ffffffffffffffff060903006cfe4f014e5442324756333164646464ffffffffffffffffffffffffffffffffffffffffffffffffffffffff00002b91
2025-07-28 10:46:54,696 - INFO - Response length: 72 bytes
2025-07-28 10:46:54,696 - INFO - Response length: 18432
2025-07-28 10:46:54,696 - INFO - Function code: 0x8404
2025-07-28 10:46:54,696 - INFO - Received program info response
2025-07-28 10:46:54,696 - INFO - Program version: 10400
2025-07-28 10:46:54,696 - INFO - Date: 2025-07-28 10:46:54
2025-07-28 10:46:54,696 - INFO - Program length: 198918 bytes
2025-07-28 10:46:54,696 - INFO - Program checksum: 0x014FFE6C
2025-07-28 10:46:54,696 - INFO - Program ID: NTB2GV31
2025-07-28 10:46:54,696 - INFO - Program info received successfully
2025-07-28 10:46:54,696 - INFO - 
=== Step 3: Simulate Download Data Request (Optional) ===
2025-07-28 10:46:54,696 - INFO - Sending message: 00 0e 91 01 00 00 00 00 04 00 a8 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 10:46:54,696 - INFO - Message length: 64 bytes
2025-07-28 10:47:04,698 - INFO - Received response: 
2025-07-28 10:47:04,698 - INFO - Response length: 0 bytes
2025-07-28 10:47:04,698 - ERROR - Failed to get download data response
2025-07-28 10:47:04,698 - INFO - Disconnected from server
2025-07-28 10:47:04,699 - ERROR - ✗ Some tests failed!
2025-07-28 11:01:35,541 - INFO - Device Message Test Script
2025-07-28 11:01:35,541 - INFO - ==================================================
2025-07-28 11:01:35,541 - INFO - Starting OTA flow test...
2025-07-28 11:01:35,543 - INFO - Connected to server localhost:8080
2025-07-28 11:01:35,543 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-28 11:01:35,543 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 11:01:35,543 - INFO - Message length: 64 bytes
2025-07-28 11:01:35,606 - INFO - Received response: 0018830228a001a1646464640000000397
2025-07-28 11:01:35,606 - INFO - Response length: 17 bytes
2025-07-28 11:01:35,606 - INFO - Response length: 24
2025-07-28 11:01:35,606 - INFO - Function code: 0x8302
2025-07-28 11:01:35,606 - INFO - Platform version: 10400
2025-07-28 11:01:35,606 - INFO - Upgrade type: 1
2025-07-28 11:01:35,606 - INFO - Upgrade control: 0xA1
2025-07-28 11:01:35,606 - INFO - Area: 100, Option: 100
2025-07-28 11:01:35,606 - INFO - Upgrade available: True
2025-07-28 11:01:35,606 - INFO - Version check passed, upgrade available
2025-07-28 11:01:35,606 - INFO - 
=== Step 2: Send Get Program Info Request (8403) ===
2025-07-28 11:01:35,606 - INFO - Sending message: 00 08 84 03 8f 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 11:01:35,606 - INFO - Message length: 64 bytes
2025-07-28 11:01:35,667 - INFO - Received response: 48008404a02819071c0b0123ffffffffffffffff060903006cfe4f014e5442324756333164646464ffffffffffffffffffffffffffffffffffffffffffffffffffffffff00002b52
2025-07-28 11:01:35,667 - INFO - Response length: 72 bytes
2025-07-28 11:01:35,667 - INFO - Response length: 18432
2025-07-28 11:01:35,667 - INFO - Function code: 0x8404
2025-07-28 11:01:35,667 - INFO - Received program info response
2025-07-28 11:01:35,667 - INFO - Program version: 10400
2025-07-28 11:01:35,667 - INFO - Date: 2025-07-28 11:01:35
2025-07-28 11:01:35,667 - INFO - Program length: 198918 bytes
2025-07-28 11:01:35,667 - INFO - Program checksum: 0x014FFE6C
2025-07-28 11:01:35,667 - INFO - Program ID: NTB2GV31
2025-07-28 11:01:35,667 - INFO - Program info received successfully
2025-07-28 11:01:35,667 - INFO - 
=== Step 3: Simulate Download Data Request (Optional) ===
2025-07-28 11:01:35,667 - INFO - Sending message: 00 0e 91 01 00 00 00 00 04 00 a4 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 11:01:35,668 - INFO - Message length: 64 bytes
2025-07-28 11:01:35,668 - INFO - Received response: 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
2025-07-28 11:01:35,668 - INFO - Response length: 1024 bytes
2025-07-28 11:01:35,668 - INFO - Response length: 3076
2025-07-28 11:01:35,668 - INFO - Function code: 0x9102
2025-07-28 11:01:35,668 - INFO - Received doanloadn data
2025-07-28 11:01:35,668 - INFO - Data content: 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
2025-07-28 11:01:35,668 - INFO - Data length: 1014 bytes
2025-07-28 11:01:35,668 - INFO - Download data received successfully
2025-07-28 11:01:35,668 - INFO - 
=== OTA Flow Test Completed Successfully ===
2025-07-28 11:01:35,668 - INFO - Disconnected from server
2025-07-28 11:01:35,668 - INFO - ✓ All tests passed!
2025-07-28 15:03:59,623 - INFO - Device Message Test Script
2025-07-28 15:03:59,623 - INFO - ==================================================
2025-07-28 15:03:59,623 - INFO - Starting OTA flow test...
2025-07-28 15:03:59,624 - INFO - Connected to server localhost:8080
2025-07-28 15:03:59,624 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-28 15:03:59,624 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 15:03:59,624 - INFO - Message length: 64 bytes
2025-07-28 15:03:59,699 - INFO - Received response: 0018830228a001a1646464640097030000
2025-07-28 15:03:59,700 - INFO - Response length: 17 bytes
2025-07-28 15:03:59,700 - INFO - Response length: 24
2025-07-28 15:03:59,700 - INFO - Function code: 0x8302
2025-07-28 15:03:59,700 - INFO - Platform version: 10400
2025-07-28 15:03:59,700 - INFO - Upgrade type: 1
2025-07-28 15:03:59,700 - INFO - Upgrade control: 0xA1
2025-07-28 15:03:59,700 - INFO - Area: 100, Option: 100
2025-07-28 15:03:59,700 - INFO - Upgrade available: True
2025-07-28 15:03:59,700 - INFO - Version check passed, upgrade available
2025-07-28 15:03:59,700 - INFO - 
=== Step 2: Send Get Program Info Request (8403) ===
2025-07-28 15:03:59,700 - INFO - Sending message: 00 08 84 03 8f 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 15:03:59,700 - INFO - Message length: 64 bytes
2025-07-28 15:03:59,700 - INFO - Received response: 00488404a02819071c0f033bffffffffffffffff060903006cfe4f014e5442324756333164646464ffffffffffffffffffffffffffffffffffffffffffffffffffffffff702b0000
2025-07-28 15:03:59,701 - INFO - Response length: 72 bytes
2025-07-28 15:03:59,701 - INFO - Response length: 72
2025-07-28 15:03:59,701 - INFO - Function code: 0x8404
2025-07-28 15:03:59,701 - INFO - Received program info response
2025-07-28 15:03:59,701 - INFO - Program version: 10400
2025-07-28 15:03:59,701 - INFO - Date: 2025-07-28 15:03:59
2025-07-28 15:03:59,701 - INFO - Program length: 198918 bytes
2025-07-28 15:03:59,701 - INFO - Program checksum: 0x014FFE6C
2025-07-28 15:03:59,701 - INFO - Program ID: NTB2GV31
2025-07-28 15:03:59,701 - INFO - Program info received successfully
2025-07-28 15:03:59,701 - INFO - 
=== Step 3: Simulate Download Data Request ===
2025-07-28 15:03:59,701 - INFO - Sending message: 00 0e 91 01 00 00 00 00 04 00 a4 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 15:03:59,701 - INFO - Message length: 64 bytes
2025-07-28 15:03:59,703 - INFO - Received response: 040c91020000000004003d271903070b1306b589944a4dc604c3c60803004ff54f014e5442324756333100000000000000000000000000000000000000000000000000000000000000000020001041eb0408fde6040801e7040805e7040807e7040809e70408000000000000000000000000000000000be704080de70408000000009362040815d00308e1f50408e5f50408e9f50408edf50408f1f50408f5f50408f9f50408fdf5040801f6040805f6040809f604080df6040811f6040815f6040819f604081df6040821f6040825f6040829f604082df604083196030831f6040835f6040839f604083df6040841f6040845f6040849f604084df60408c3ea020851f6040855f6040859f604085df6040861f6040865f6040869f60408fb4103081b4203083b4203086df6040871f6040875f6040879f604087df6040881f6040885f6040889f604088df6040891f6040895f6040899f604085b4203089df60408a1f60408a5f60408a9f60408adf60408b1f60408b5f60408b9f60408297e0308bdf60408c1f6040863a50308c5f60408c9f60408cdf60408d1f60408d5f60408d9f60408ddf60408e1f60408e5f60408e9f60408edf60408f1f60408f5f60408f9f60408fdf6040801f7040805f7040802b49df8011001709df80010417001b0704702b49df8031001709df8021041709df8011081709df80010c17001b070470100dff80c29100089b2dff8082d92898a1a00eb420070474ff48071dff8f42c91817c21dff8ec2cd181dff8e81c08827047dff8e02c90817c22dff8d83cda81dff8d02c11827047dff8cc0cb0f83e0290f0aa00c0b2704710b54ff4fa714020fff7e7ffdff8a84801212000fff7b0ffa41c4420fff7c4ffdff898180860dff8980890f80602c0f38100c0b2002853d1fff7daff002807d0dff88018dff87408006805f0f1ff06e0dff87418dff86408006805f0e9ffdff86c080078012805d12e20dff850180968087224e0dff85408007802280dd12e20dff8381809688872dff830080068807adff828180968087211e0dff83008007803280cd12e20dff8141809680873dff80c1809688872dff8041809680872dff80c080078401c042190fbf1f201fb1200dff8f81708700020dff8e81b08703ce0dff8dc0790f80602c0f38100c0b2012833d1dff8d00b00780b2802db05f0ffff2be0dff8c00b0078072813dbfff770ff002807d0dff8ac1adff8a007006805f087ff1ae0dff8a01adff89007006805f07fff12e0fff75cff002807d0dff88c1adff87807006805f073ff06e0dff8801adff86807006805f06bffdff8680b0078401cdff8601b087010bd80b54ff47a70fff726ff0121542009f0d2ff0020dff84c1b087001bd70471cb54ff4fa714ff41260fff722ffdff82047dff8340bb0f89c003c2190fb
2025-07-28 15:03:59,703 - INFO - Response length: 1024 bytes
2025-07-28 15:03:59,703 - INFO - Response length: 1036
2025-07-28 15:03:59,703 - INFO - Function code: 0x9102
2025-07-28 15:03:59,703 - INFO - Received doanloadn data
2025-07-28 15:03:59,703 - INFO - Data length: 1014 bytes
2025-07-28 15:03:59,703 - INFO - Start to download data received successfully
2025-07-28 15:03:59,703 - INFO - Sending message: 00 0e 91 01 00 08 03 00 04 00 af 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 15:03:59,703 - INFO - Message length: 64 bytes
2025-07-28 15:03:59,703 - INFO - Received response: f1f0010089b22000fff7cfa10100
2025-07-28 15:03:59,703 - INFO - Response length: 14 bytes
2025-07-28 15:03:59,703 - INFO - Response length: 61936
2025-07-28 15:03:59,703 - INFO - Function code: 0x0100
2025-07-28 15:03:59,703 - ERROR - Failed to end to download data
2025-07-28 15:03:59,703 - INFO - Disconnected from server
2025-07-28 15:03:59,703 - ERROR - ✗ Some tests failed!
2025-07-28 15:24:00,844 - INFO - Device Message Test Script
2025-07-28 15:24:00,844 - INFO - ==================================================
2025-07-28 15:24:00,844 - INFO - Starting OTA flow test...
2025-07-28 15:24:00,845 - INFO - Connected to server localhost:8080
2025-07-28 15:24:00,845 - INFO - 
=== Step 1: Send Get Version Request (8301) ===
2025-07-28 15:24:00,845 - INFO - Sending message: 00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 15:24:00,846 - INFO - Message length: 64 bytes
2025-07-28 15:24:00,913 - INFO - Received response: 0018830228a001a1646464640097030000
2025-07-28 15:24:00,913 - INFO - Response length: 17 bytes
2025-07-28 15:24:00,913 - INFO - Response length: 24
2025-07-28 15:24:00,913 - INFO - Function code: 0x8302
2025-07-28 15:24:00,913 - INFO - Platform version: 10400
2025-07-28 15:24:00,913 - INFO - Upgrade type: 1
2025-07-28 15:24:00,913 - INFO - Upgrade control: 0xA1
2025-07-28 15:24:00,913 - INFO - Area: 100, Option: 100
2025-07-28 15:24:00,913 - INFO - Upgrade available: True
2025-07-28 15:24:00,913 - INFO - Version check passed, upgrade available
2025-07-28 15:24:00,913 - INFO - 
=== Step 2: Send Get Program Info Request (8403) ===
2025-07-28 15:24:00,913 - INFO - Sending message: 00 08 84 03 8f 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 15:24:00,913 - INFO - Message length: 64 bytes
2025-07-28 15:24:00,914 - INFO - Received response: 00488404a02819071c0f1800ffffffffffffffff060903006cfe4f014e5442324756333164646464ffffffffffffffffffffffffffffffffffffffffffffffffffffffff4a2b0000
2025-07-28 15:24:00,914 - INFO - Response length: 72 bytes
2025-07-28 15:24:00,914 - INFO - Response length: 72
2025-07-28 15:24:00,914 - INFO - Function code: 0x8404
2025-07-28 15:24:00,914 - INFO - Received program info response
2025-07-28 15:24:00,914 - INFO - Program version: 10400
2025-07-28 15:24:00,914 - INFO - Date: 2025-07-28 15:24:00
2025-07-28 15:24:00,914 - INFO - Program length: 198918 bytes
2025-07-28 15:24:00,914 - INFO - Program checksum: 0x014FFE6C
2025-07-28 15:24:00,914 - INFO - Program ID: NTB2GV31
2025-07-28 15:24:00,914 - INFO - Program info received successfully
2025-07-28 15:24:00,914 - INFO - 
=== Step 3: Simulate Download Data Request ===
2025-07-28 15:24:00,914 - INFO - Sending message: 00 0e 91 01 00 00 00 00 04 00 a4 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 15:24:00,914 - INFO - Message length: 64 bytes
2025-07-28 15:24:00,916 - INFO - Received response: 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
2025-07-28 15:24:00,916 - INFO - Response length: 1024 bytes
2025-07-28 15:24:00,916 - INFO - Response length: 1038
2025-07-28 15:24:00,916 - INFO - Function code: 0x9102
2025-07-28 15:24:00,916 - INFO - Received doanloadn data
2025-07-28 15:24:00,916 - INFO - Data length: 1014 bytes
2025-07-28 15:24:00,916 - INFO - Start to download data received successfully
2025-07-28 15:24:00,916 - INFO - Sending message: 00 0e 91 01 00 08 03 00 04 00 af 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-07-28 15:24:00,916 - INFO - Message length: 64 bytes
2025-07-28 15:24:00,916 - INFO - Received response: f1f0010089b22000fff7d1a10100
2025-07-28 15:24:00,916 - INFO - Response length: 14 bytes
2025-07-28 15:24:00,916 - INFO - Response length: 61936
2025-07-28 15:24:00,916 - INFO - Function code: 0x0100
2025-07-28 15:24:00,916 - ERROR - Failed to end to download data
2025-07-28 15:24:00,916 - INFO - Disconnected from server
2025-07-28 15:24:00,916 - ERROR - ✗ Some tests failed!
