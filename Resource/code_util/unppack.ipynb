import struct

# 将 hex 字符串转换为 bytes
hex_string = '00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00'
hex_bytes = bytes.fromhex(hex_string)

test =
'00 0e 91 01 00 00 00 00 '
'04 00 a4 00 00 00 00 00 '
'00 00 00 00 00 00 00 00 '
'00 00 00 00 00 00 00 00 '
'00 00 00 00 00 00 00 00 '
'00 00 00 00 00 00 00 00 '
'00 00 00 00 00 00 00 00 '
'00 00 00 00 00 00 00 00'
len(test)


# 提取前两个字节作为长度字段
length_data = hex_bytes[0:2]
length = struct.unpack('>H', length_data)[0]

print(f"Length bytes: {length_data.hex()} → Parsed length: {length}")

func_code = struct.unpack('>H', hex_bytes[2:4])[0]  # 大端
func_code== 0x8301

 device_version =struct.unpack('>H', hex_bytes[28:30])[0]
 device_version

device_number = hex_bytes[30:46].decode('ascii', errors='ignore').strip('\x00')
device_number

hex_bytes[46:50]

checksum = struct.unpack('>I', hex_bytes[46:50])[0]
checksum

cal_checkesun =sum(hex_bytes[0:46]) & 0xFFFFFFFF
cal_checkesun

program_id = hex_bytes[4:12].decode('ascii', errors='ignore').strip('\x00')
program_id


hex_string_data = ' 00 0e 91 01 00 08 03 00 04 00 af 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00'
data_hex = bytes.fromhex(hex_string_data)

test_receiv = "04 0c 91 02 00 00 00 00 04 00"

length = struct.unpack('>H', data_hex[0:2])[0]
func_code = struct.unpack('>H', data_hex[2:4])[0]
data_index = struct.unpack('>I', data_hex[4:8])[0]
request_length = struct.unpack('>H', data_hex[8:10])[0]
#checksum = struct.unpack('<I', data_hex[10:14])[0]

request_length

data_hex = bytes.fromhex(test_receiv)

data_hex

sum(data_hex[0:10]) & 0xFFFFFFFF

data_hex[10:14]

struct.unpack('<I', data_hex[10:14])[0]

length = struct.unpack('>H', data_hex[0:2])[0]
func_code = struct.unpack('>H', data_hex[2:4])[0]
data_index = struct.unpack('>I', data_hex[4:8])[0]
request_length = struct.unpack('>H', data_hex[8:10])[0]
checksum = struct.unpack('<I', data_hex[10:14])[0]

#data_index = struct.unpack('<I', data_hex[4:8])[0]
data_index

length

func_code

request_length

checksum

data8301 = "00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"



data = bytes.fromhex(data8301)
checksum_calculated = sum(data[0:46]) & 0xFFFFFFFF
checksum_in_msg = struct.unpack('>I', data[46:50])[0]

print(f"计算校验和: {checksum_calculated}")
print(f"报文校验和: {checksum_in_msg}")

cal_len = struct.pack('>H', 1024)
cal_len