{"cells": [{"cell_type": "code", "execution_count": 2, "id": "1a48ca26", "metadata": {}, "outputs": [], "source": ["import struct"]}, {"cell_type": "code", "execution_count": 10, "id": "8f5efe54", "metadata": {}, "outputs": [], "source": ["# 将 hex 字符串转换为 bytes\n", "hex_string = '00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00'\n", "hex_bytes = bytes.fromhex(hex_string)"]}, {"cell_type": "code", "execution_count": null, "id": "8dc164d5", "metadata": {}, "outputs": [{"data": {"text/plain": ["191"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["test =\n", "'00 0e 91 01 00 00 00 00 '\n", "'04 00 a4 00 00 00 00 00 '\n", "'00 00 00 00 00 00 00 00 '\n", "'00 00 00 00 00 00 00 00 '\n", "'00 00 00 00 00 00 00 00 '\n", "'00 00 00 00 00 00 00 00 '\n", "'00 00 00 00 00 00 00 00 '\n", "'00 00 00 00 00 00 00 00'\n", "len(test)\n"]}, {"cell_type": "code", "execution_count": null, "id": "a1096796", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Length bytes: 0032 → Parsed length: (50,)\n"]}], "source": ["# 提取前两个字节作为长度字段\n", "length_data = hex_bytes[0:2]\n", "length = struct.unpack('>H', length_data)[0]\n", "\n", "print(f\"Length bytes: {length_data.hex()} → Parsed length: {length}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "310338ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["func_code = struct.unpack('>H', hex_bytes[2:4])[0]  # 大端\n", "func_code== 0x8301"]}, {"cell_type": "code", "execution_count": 16, "id": "a06c877b", "metadata": {}, "outputs": [{"data": {"text/plain": ["10000"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": [" device_version =struct.unpack('>H', hex_bytes[28:30])[0]\n", " device_version"]}, {"cell_type": "code", "execution_count": 9, "id": "8a56328c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'3402024203007157'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["device_number = hex_bytes[30:46].decode('ascii', errors='ignore').strip('\\x00')\n", "device_number"]}, {"cell_type": "code", "execution_count": null, "id": "6979194a", "metadata": {}, "outputs": [{"data": {"text/plain": ["b'\\xb0\\x13\\x00\\x00'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["hex_bytes[46:50]"]}, {"cell_type": "code", "execution_count": 10, "id": "34c30a33", "metadata": {}, "outputs": [{"data": {"text/plain": ["2954035200"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["checksum = struct.unpack('>I', hex_bytes[46:50])[0]\n", "checksum"]}, {"cell_type": "code", "execution_count": 11, "id": "3ecde2f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["5040"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["cal_checkesun =sum(hex_bytes[0:46]) & 0xFFFFFFFF\n", "cal_checkesun"]}, {"cell_type": "code", "execution_count": 14, "id": "af481a17", "metadata": {}, "outputs": [{"data": {"text/plain": ["'NTB2GV31'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["program_id = hex_bytes[4:12].decode('ascii', errors='ignore').strip('\\x00')\n", "program_id\n"]}, {"cell_type": "code", "execution_count": 34, "id": "49d99746", "metadata": {}, "outputs": [], "source": ["hex_string_data = '00 0e 91 01 00 04 00 00 04 00 a8 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00'\n", "data_hex = bytes.fromhex(hex_string_data)"]}, {"cell_type": "code", "execution_count": 2, "id": "311699f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["b'\\x00\\x0e\\x91\\x01\\x00\\xf4\\x01\\x00\\x04\\x00\\x99\\x01\\x00\\x00'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data_hex"]}, {"cell_type": "code", "execution_count": 35, "id": "42718ec2", "metadata": {}, "outputs": [{"data": {"text/plain": ["168"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(data_hex[0:10]) & 0xFFFFFFFF"]}, {"cell_type": "code", "execution_count": 36, "id": "7c693956", "metadata": {}, "outputs": [{"data": {"text/plain": ["b'\\xa8\\x00\\x00\\x00'"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["data_hex[10:14]"]}, {"cell_type": "code", "execution_count": 38, "id": "8841f489", "metadata": {}, "outputs": [{"data": {"text/plain": ["168"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["struct.unpack('<I', data_hex[10:14])[0]"]}, {"cell_type": "code", "execution_count": 39, "id": "14f1ce04", "metadata": {}, "outputs": [], "source": ["length = struct.unpack('>H', data_hex[0:2])[0]\n", "func_code = struct.unpack('>H', data_hex[2:4])[0]\n", "data_index = struct.unpack('>I', data_hex[4:8])[0]\n", "request_length = struct.unpack('>H', data_hex[8:10])[0]\n", "checksum = struct.unpack('<I', data_hex[10:14])[0]"]}, {"cell_type": "code", "execution_count": 40, "id": "47822ea1", "metadata": {}, "outputs": [{"data": {"text/plain": ["1024"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["data_index = struct.unpack('<I', data_hex[4:8])[0]\n", "data_index"]}, {"cell_type": "code", "execution_count": 42, "id": "7ed07156", "metadata": {}, "outputs": [{"data": {"text/plain": ["14"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["length"]}, {"cell_type": "code", "execution_count": 43, "id": "19e290f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["37121"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["func_code"]}, {"cell_type": "code", "execution_count": 44, "id": "ba20b13e", "metadata": {}, "outputs": [{"data": {"text/plain": ["1024"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["request_length"]}, {"cell_type": "code", "execution_count": 19, "id": "252ae470", "metadata": {}, "outputs": [{"data": {"text/plain": ["2566979584"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["checksum"]}, {"cell_type": "code", "execution_count": 5, "id": "46b32b58", "metadata": {}, "outputs": [], "source": ["data8301 = \"00 32 83 01 4e 54 42 32 47 56 33 31 64 64 64 64 ff ff ff ff ff ff ff ff ff ff ff ff 27 10 33 34 30 32 30 32 34 32 30 33 30 30 37 31 35 37 b0 13 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\""]}, {"cell_type": "code", "execution_count": null, "id": "54f92d29", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "id": "74a450de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["计算校验和: 5040\n", "报文校验和: 2954035200\n"]}], "source": ["data = bytes.fromhex(data8301)\n", "checksum_calculated = sum(data[0:46]) & 0xFFFFFFFF\n", "checksum_in_msg = struct.unpack('>I', data[46:50])[0]\n", "\n", "print(f\"计算校验和: {checksum_calculated}\")\n", "print(f\"报文校验和: {checksum_in_msg}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "15324223", "metadata": {}, "outputs": [{"data": {"text/plain": ["b'\\x04\\x00'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["cal_len = struct.pack('>H', 1024)\n", "cal_len"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}