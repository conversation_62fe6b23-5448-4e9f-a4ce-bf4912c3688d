#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证OTASID重构结果
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ufn_protocol.constants import OTASID

def main():
    print("=== OTASID重构验证 ===")
    
    # 测试CCU
    ccu_bytes = b'NTB2GV31'
    ccu_grpc = OTASID.get_grpc_value(ccu_bytes)
    print(f"CCU: {ccu_bytes} -> gRPC值: {ccu_grpc}")
    
    # 测试BMS
    bms_bytes = b'RNBMSV20'
    bms_grpc = OTASID.get_grpc_value(bms_bytes)
    print(f"BMS: {bms_bytes} -> gRPC值: {bms_grpc}")
    
    # 测试反向转换
    ccu_back = OTASID.get_byte_by_grpc_value(1)
    bms_back = OTASID.get_byte_by_grpc_value(2)
    print(f"gRPC值1 -> {ccu_back}")
    print(f"gRPC值2 -> {bms_back}")
    
    print("✅ OTASID重构成功完成！")

if __name__ == '__main__':
    main()
