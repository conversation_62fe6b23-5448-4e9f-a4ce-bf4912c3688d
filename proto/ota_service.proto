syntax = "proto3";

package ota;

enum ProgramType {
    UNKNOWN = 0;
    CCU = 1;
    BMS = 2;
}

enum DeviceUpgradeStatus {
    UPGRADE_UNKNOWN = 0;
    UPGRADE_STARTED = 1;    // 升级中
    UPGRADE_ENDED = 2;  // 升级结束
    UPGRADE_COMPLETED = 3;     // 升级失败
}

// OTA升级服务
service OTAService {
    // 查询升级包信息
    rpc QueryUpgradeInfo(UpgradeInfoRequest) returns (UpgradeInfoResponse);

    // 更新设备升级状态
    rpc UpdateDeviceStatus(DeviceStatusRequest) returns (DeviceStatusResponse);
}

// 升级信息请求
message UpgradeInfoRequest {
    string device_id = 1;       // 设备编码
    ProgramType program_type = 2;     
}

// 升级信息响应
message UpgradeInfoResponse {
    string device_id = 1;       // 设备编码
    string file_url = 2;        // 文件地址
    ProgramType program_type = 3;      // 升级程序类型
    string platform_version = 4; // 平台版本
    bool available = 5;         // 是否有可用升级
    bytes file_data = 6;        // 文件二进制数据（如果有）
}

// 设备状态更新请求
message DeviceStatusRequest {
    string device_id = 1;           // 设备编码
    DeviceUpgradeStatus status = 2; // 升级状态
    string message = 3;             // 状态描述信息（可选）
}

// 设备状态更新响应
message DeviceStatusResponse {
    bool success = 1;               // 更新是否成功
    string message = 2;             // 响应消息
}

