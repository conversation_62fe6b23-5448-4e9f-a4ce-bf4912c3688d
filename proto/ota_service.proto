syntax = "proto3";

package ota;

enum ProgramType {
    UNKNOWN = 0;
    CCU = 1;
    BMS = 2;
}
// OTA升级服务
service OTAService {
    // 查询升级包信息
    rpc QueryUpgradeInfo(UpgradeInfoRequest) returns (UpgradeInfoResponse);

    
}

// 升级信息请求
message UpgradeInfoRequest {
    string device_id = 1;       // 设备编码
    ProgramType program_type = 2;     
}

// 升级信息响应
message UpgradeInfoResponse {
    string device_id = 1;       // 设备编码
    string file_url = 2;        // 文件地址
    ProgramType program_type = 3;      // 升级程序类型
    string platform_version = 4; // 平台版本
    bool available = 5;         // 是否有可用升级
    bytes file_data = 6;        // 文件二进制数据（如果有）
}

