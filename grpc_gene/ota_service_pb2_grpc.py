# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import ota_service_pb2 as proto_dot_ota__service__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in proto/ota_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class OTAServiceStub(object):
    """OTA升级服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.QueryUpgradeInfo = channel.unary_unary(
                '/ota.OTAService/QueryUpgradeInfo',
                request_serializer=proto_dot_ota__service__pb2.UpgradeInfoRequest.SerializeToString,
                response_deserializer=proto_dot_ota__service__pb2.UpgradeInfoResponse.FromString,
                _registered_method=True)
        self.UpdateDeviceStatus = channel.unary_unary(
                '/ota.OTAService/UpdateDeviceStatus',
                request_serializer=proto_dot_ota__service__pb2.DeviceStatusRequest.SerializeToString,
                response_deserializer=proto_dot_ota__service__pb2.DeviceStatusResponse.FromString,
                _registered_method=True)


class OTAServiceServicer(object):
    """OTA升级服务
    """

    def QueryUpgradeInfo(self, request, context):
        """查询升级包信息
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateDeviceStatus(self, request, context):
        """更新设备升级状态
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OTAServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'QueryUpgradeInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.QueryUpgradeInfo,
                    request_deserializer=proto_dot_ota__service__pb2.UpgradeInfoRequest.FromString,
                    response_serializer=proto_dot_ota__service__pb2.UpgradeInfoResponse.SerializeToString,
            ),
            'UpdateDeviceStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateDeviceStatus,
                    request_deserializer=proto_dot_ota__service__pb2.DeviceStatusRequest.FromString,
                    response_serializer=proto_dot_ota__service__pb2.DeviceStatusResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'ota.OTAService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('ota.OTAService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OTAService(object):
    """OTA升级服务
    """

    @staticmethod
    def QueryUpgradeInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/ota.OTAService/QueryUpgradeInfo',
            proto_dot_ota__service__pb2.UpgradeInfoRequest.SerializeToString,
            proto_dot_ota__service__pb2.UpgradeInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateDeviceStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/ota.OTAService/UpdateDeviceStatus',
            proto_dot_ota__service__pb2.DeviceStatusRequest.SerializeToString,
            proto_dot_ota__service__pb2.DeviceStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
