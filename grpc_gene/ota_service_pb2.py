# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: proto/ota_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'proto/ota_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17proto/ota_service.proto\x12\x03ota\"O\n\x12UpgradeInfoRequest\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12&\n\x0cprogram_type\x18\x02 \x01(\x0e\x32\x10.ota.ProgramType\"\xa2\x01\n\x13UpgradeInfoResponse\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12\x10\n\x08\x66ile_url\x18\x02 \x01(\t\x12&\n\x0cprogram_type\x18\x03 \x01(\x0e\x32\x10.ota.ProgramType\x12\x18\n\x10platform_version\x18\x04 \x01(\t\x12\x11\n\tavailable\x18\x05 \x01(\x08\x12\x11\n\tfile_data\x18\x06 \x01(\x0c\"c\n\x13\x44\x65viceStatusRequest\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12(\n\x06status\x18\x02 \x01(\x0e\x32\x18.ota.DeviceUpgradeStatus\x12\x0f\n\x07message\x18\x03 \x01(\t\"8\n\x14\x44\x65viceStatusResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t*,\n\x0bProgramType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x07\n\x03\x43\x43U\x10\x01\x12\x07\n\x03\x42MS\x10\x02*V\n\x13\x44\x65viceUpgradeStatus\x12\x13\n\x0fUPGRADE_UNKNOWN\x10\x00\x12\x13\n\x0fUPGRADE_STARTED\x10\x01\x12\x15\n\x11UPGRADE_COMPLETED\x10\x02\x32\x9e\x01\n\nOTAService\x12\x45\n\x10QueryUpgradeInfo\x12\x17.ota.UpgradeInfoRequest\x1a\x18.ota.UpgradeInfoResponse\x12I\n\x12UpdateDeviceStatus\x12\x18.ota.DeviceStatusRequest\x1a\x19.ota.DeviceStatusResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'proto.ota_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_PROGRAMTYPE']._serialized_start=437
  _globals['_PROGRAMTYPE']._serialized_end=481
  _globals['_DEVICEUPGRADESTATUS']._serialized_start=483
  _globals['_DEVICEUPGRADESTATUS']._serialized_end=569
  _globals['_UPGRADEINFOREQUEST']._serialized_start=32
  _globals['_UPGRADEINFOREQUEST']._serialized_end=111
  _globals['_UPGRADEINFORESPONSE']._serialized_start=114
  _globals['_UPGRADEINFORESPONSE']._serialized_end=276
  _globals['_DEVICESTATUSREQUEST']._serialized_start=278
  _globals['_DEVICESTATUSREQUEST']._serialized_end=377
  _globals['_DEVICESTATUSRESPONSE']._serialized_start=379
  _globals['_DEVICESTATUSRESPONSE']._serialized_end=435
  _globals['_OTASERVICE']._serialized_start=572
  _globals['_OTASERVICE']._serialized_end=730
# @@protoc_insertion_point(module_scope)
