#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gRPC服务器，用于测试OTA升级服务
"""

import os
import sys
import logging
import grpc
from concurrent import futures
from typing import Dict, Optional

# # 添加grpc_generated到路径
# sys.path.append(os.path.join(os.path.dirname(__file__), 'grpc_generated'))

# 添加grpc_gene到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'grpc_gene'))

import ota_service_pb2
import ota_service_pb2_grpc

logger = logging.getLogger(__name__)


class OTAServiceImpl(ota_service_pb2_grpc.OTAServiceServicer):
    """OTA服务实现"""
    
    def __init__(self):
        # 模拟升级包数据库
        self.upgrade_packages = {
            1: {
                'device_id': '340202420300715',
                'file_url': 'Resource/4c4e96cf-9937-4299-b113-5948bb7f2e53_ccu-10045_NT-B2G-V3 2024.7.10_Ex.yxp',
                'program_id': 'NTB2GV31',
                'platform_version': '10400',
                'available': True
            },
            2: {
                'device_id': '340202420300715',
                'file_url': 'Resource/bms_upgrade_package.bin',
                'program_id': 'RNBMSV20', 
                'platform_version': '10200',
                'available': True
            }
        }
        
        # 文件缓存
        self.file_cache: Dict[str, bytes] = {}
    
    def QueryUpgradeInfo(self, request, context):
        """查询升级包信息"""
        logger.info(f"Received upgrade info query: device_id={request.device_id}, program_type={request.program_type}")

        # 查找升级包 - 需要根据 program_type 转换为对应的字符串键
        program_type_name = ota_service_pb2.ProgramType.Name(request.program_type)
        if program_type_name == 'CCU':
            program_key = 1
        elif program_type_name == 'BMS':
            program_key = 2
        else:
            program_key = None

        upgrade_info = self.upgrade_packages.get(program_key) if program_key else None

        if upgrade_info and upgrade_info['available']:
            # 直接加载文件数据
            file_data = b""
            file_url = upgrade_info['file_url']

            try:
                # 检查缓存
                if file_url in self.file_cache:
                    file_data = self.file_cache[file_url]
                    logger.info(f"Using cached file data, size: {len(file_data)} bytes")
                else:
                    # 从文件系统读取
                    if os.path.exists(file_url):
                        with open(file_url, 'rb') as f:
                            file_data = f.read()

                        # 缓存文件数据
                        self.file_cache[file_url] = file_data
                        logger.info(f"Loaded file from disk: {file_url}, size: {len(file_data)} bytes")
                    else:
                        # 文件不存在，返回模拟数据
                        file_data = b"MOCK_UPGRADE_DATA_" * 1000  # 模拟升级包数据
                        logger.warning(f"File not found: {file_url}, returning mock data")
            except Exception as e:
                logger.error(f"Failed to load file {file_url}: {e}")
                file_data = b"MOCK_UPGRADE_DATA_" * 1000  # 模拟升级包数据

            response = ota_service_pb2.UpgradeInfoResponse(
                device_id=request.device_id,
                file_url=upgrade_info['file_url'],
                program_type=request.program_type,  # 使用 program_type 而不是 program_id
                platform_version=upgrade_info['platform_version'],
                available=True,
                file_data=file_data  # 直接包含文件数据
            )
            logger.info(f"Found upgrade package with file data: {len(file_data)} bytes")
        else:
            response = ota_service_pb2.UpgradeInfoResponse(
                device_id=request.device_id,
                file_url='',
                program_type=request.program_type,  # 使用 program_type 而不是 program_id
                platform_version='0',
                available=False,
                file_data=b""
            )
            logger.info(f"No upgrade package found for program_id: {request.program_type}")

        return response
    
    # def DownloadFileContent(self, request, context):
    #     """下载文件内容"""
    #     logger.info(f"Received file download request: {request.file_url}")
        
    #     try:
    #         # 检查缓存
    #         if request.file_url in self.file_cache:
    #             file_data = self.file_cache[request.file_url]
    #             logger.info(f"Returning cached file data, size: {len(file_data)} bytes")
    #         else:
    #             # 从文件系统读取
    #             if os.path.exists(request.file_url):
    #                 with open(request.file_url, 'rb') as f:
    #                     file_data = f.read()
                    
    #                 # 缓存文件数据
    #                 self.file_cache[request.file_url] = file_data
    #                 logger.info(f"Loaded file from disk: {request.file_url}, size: {len(file_data)} bytes")
    #             else:
    #                 # 文件不存在，返回模拟数据
    #                 file_data = b"MOCK_UPGRADE_DATA_" * 1000  # 模拟升级包数据
    #                 logger.warning(f"File not found: {request.file_url}, returning mock data")
            
    #         response = ota_service_pb2.FileDownloadResponse(
    #             file_data=file_data,
    #             success=True,
    #             error_message=""
    #         )
            
    #     except Exception as e:
    #         logger.error(f"Failed to download file {request.file_url}: {e}")
    #         response = ota_service_pb2.FileDownloadResponse(
    #             file_data=b"",
    #             success=False,
    #             error_message=str(e)
    #         )
        
    #     return response
    
    # def QueryFileSize(self, request, context):
        """查询文件大小"""
        logger.info(f"Received file size query: {request.file_url}")
        
        try:
            if os.path.exists(request.file_url):
                file_size = os.path.getsize(request.file_url)
                logger.info(f"File size: {file_size} bytes")
                
                response = ota_service_pb2.FileSizeResponse(
                    file_size=file_size,
                    success=True,
                    error_message=""
                )
            else:
                # 文件不存在，返回模拟大小
                mock_size = 18000  # 模拟18KB
                logger.warning(f"File not found: {request.file_url}, returning mock size: {mock_size}")
                
                response = ota_service_pb2.FileSizeResponse(
                    file_size=mock_size,
                    success=True,
                    error_message=""
                )
                
        except Exception as e:
            logger.error(f"Failed to query file size {request.file_url}: {e}")
            response = ota_service_pb2.FileSizeResponse(
                file_size=0,
                success=False,
                error_message=str(e)
            )
        
        return response
    
    def UpdateDeviceStatus(self, request, context):
        response = ota_service_pb2.DeviceStatusResponse(
            success=True,
            message="received"
        )


def serve(port=50051):
    """启动gRPC服务器"""
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    ota_service_pb2_grpc.add_OTAServiceServicer_to_server(OTAServiceImpl(), server)
    
    listen_addr = f'[::]:{port}'
    server.add_insecure_port(listen_addr)
    
    logger.info(f"Starting gRPC server on {listen_addr}")
    server.start()
    
    try:
        server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("Shutting down gRPC server...")
        server.stop(0)


if __name__ == '__main__':
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    serve()
