#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备状态更新功能
"""

import sys
import os
import logging
import threading
import time
import socket
import struct

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ufn_protocol.server import UFNServer
from ufn_protocol.grpc_client import DeviceUpgradeStatus
from ufn_protocol.constants import FunctionCode

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockGRPCServer:
    """模拟gRPC服务器，记录状态更新调用"""
    
    def __init__(self):
        self.status_updates = []
    
    def update_device_status(self, device_id: str, status: int, message: str = ""):
        """记录状态更新"""
        update_record = {
            'device_id': device_id,
            'status': status,
            'message': message,
            'timestamp': time.time()
        }
        self.status_updates.append(update_record)
        logger.info(f"Mock gRPC: Device {device_id} status updated to {status}: {message}")
        return True
    
    def get_status_updates(self):
        """获取所有状态更新记录"""
        return self.status_updates.copy()
    
    def clear_status_updates(self):
        """清除状态更新记录"""
        self.status_updates.clear()


class MockGRPCClient:
    """模拟gRPC客户端，包含状态更新功能"""
    
    def __init__(self, mock_server: MockGRPCServer):
        self.mock_server = mock_server
    
    def query_upgrade_info(self, device_id: str, program_id: bytes):
        """模拟查询升级信息"""
        logger.info(f"Mock gRPC: query_upgrade_info({device_id}, {program_id})")
        
        # 模拟返回数据
        if program_id == b'NTB2GV31':
            mock_file_data = b"MOCK_CCU_UPGRADE_DATA_" * 50  # 1100 字节
            return {
                'device_id': device_id,
                'file_url': 'mock://ccu_upgrade.bin',
                'program_id': program_id,
                'platform_version': '10400',
                'available': True,
                'file_data': mock_file_data
            }
        return None
    
    def update_device_status(self, device_id: str, status: int, message: str = ""):
        """调用模拟服务器的状态更新"""
        return self.mock_server.update_device_status(device_id, status, message)


def create_get_version_request(device_number: str, program_id: bytes, device_version: int):
    """创建获取版本信息请求"""
    request = bytearray()
    request.extend(struct.pack('>H', 0x0032))  # 报文长度 50字节
    request.extend(struct.pack('>H', FunctionCode.GET_VERSION_REQ.value))  # 功能代码 8301
    request.extend(program_id.ljust(8, b'\x00'))  # 程序ID (8字节)
    request.extend(struct.pack('B', 0))  # Area
    request.extend(struct.pack('B', 0))  # Option
    request.extend(struct.pack('B', 0))  # Mark1
    request.extend(struct.pack('B', 0))  # Mark2
    request.extend(b'\x00' * 12)  # Chip ID (12字节)
    request.extend(struct.pack('>H', device_version))  # 设备版本 (2字节)
    request.extend(device_number.encode('ascii')[:16].ljust(16, b'\x00'))  # 设备编号 (16字节)

    # 计算检验和
    checksum = sum(request) & 0xFFFFFFFF
    request.extend(struct.pack('<I', checksum))

    return bytes(request)


def create_get_program_info_request():
    """创建获取程序信息请求"""
    request = bytearray()
    request.extend(struct.pack('>H', 0x0008))  # 报文长度 8字节
    request.extend(struct.pack('>H', FunctionCode.GET_PROGRAM_INFO_REQ.value))  # 功能代码 8403

    # 计算检验和
    checksum = sum(request) & 0xFFFFFFFF
    request.extend(struct.pack('<I', checksum))

    return bytes(request)


def create_download_request(data_index: int, request_length: int):
    """创建下载程序数据请求"""
    request = bytearray()
    request.extend(struct.pack('>H', 0x000E))  # 报文长度 14字节
    request.extend(struct.pack('>H', FunctionCode.DOWNLOAD_DATA_REQ.value))  # 功能代码 9101
    request.extend(struct.pack('>I', data_index))  # 数据索引
    request.extend(struct.pack('>H', request_length))  # 请求长度
    
    # 计算检验和
    checksum = sum(request) & 0xFFFFFFFF
    request.extend(struct.pack('<I', checksum))
    
    return bytes(request)


def test_device_status_update():
    """测试设备状态更新功能"""
    logger.info("Starting device status update test...")
    
    # 创建模拟gRPC服务器和客户端
    mock_grpc_server = MockGRPCServer()
    mock_grpc_client = MockGRPCClient(mock_grpc_server)
    
    # 创建UFN服务器，使用模拟的gRPC客户端
    server = UFNServer(host='localhost', port=8081, timeout=30)
    server.grpc_client = mock_grpc_client
    server.client_handler.grpc_client = mock_grpc_client
    
    # 在单独线程中启动服务器
    server_thread = threading.Thread(target=server.start)
    server_thread.daemon = True
    server_thread.start()
    
    # 等待服务器启动
    time.sleep(1)
    
    try:
        # 连接到服务器
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect(('localhost', 8081))
        
        device_number = "340202420300715"
        program_id = b'NTB2GV31'
        
        # 1. 发送获取版本信息请求
        logger.info("Step 1: Sending get version request...")
        version_request = create_get_version_request(device_number, program_id, 10300)
        client_socket.send(version_request)
        
        # 接收响应
        response = client_socket.recv(1024)
        logger.info(f"Received version response: {len(response)} bytes")
        
        # 2. 发送获取程序信息请求
        logger.info("Step 2: Sending get program info request...")
        program_info_request = create_get_program_info_request()
        client_socket.send(program_info_request)
        
        # 接收响应
        response = client_socket.recv(1024)
        logger.info(f"Received program info response: {len(response)} bytes")
        
        # 3. 发送第一次下载数据请求（应该触发升级开始状态）
        logger.info("Step 3: Sending first download request...")
        download_request1 = create_download_request(0, 500)  # 从索引0开始，请求500字节
        client_socket.send(download_request1)
        
        # 接收响应
        response = client_socket.recv(1024)
        logger.info(f"Received first download response: {len(response)} bytes")
        
        # 4. 发送第二次下载数据请求
        logger.info("Step 4: Sending second download request...")
        download_request2 = create_download_request(500, 500)  # 从索引500开始，请求500字节
        client_socket.send(download_request2)
        
        # 接收响应
        response = client_socket.recv(1024)
        logger.info(f"Received second download response: {len(response)} bytes")
        
        # 5. 发送最后一次下载数据请求（应该触发升级结束状态）
        logger.info("Step 5: Sending final download request...")
        download_request3 = create_download_request(1000, 500)  # 从索引1000开始，请求500字节（但只有100字节可用）
        client_socket.send(download_request3)
        
        # 接收响应
        response = client_socket.recv(1024)
        logger.info(f"Received final download response: {len(response)} bytes")
        
        # 等待一下确保所有状态更新都被处理
        time.sleep(1)
        
        # 检查状态更新记录
        status_updates = mock_grpc_server.get_status_updates()
        logger.info(f"Total status updates recorded: {len(status_updates)}")
        
        for i, update in enumerate(status_updates):
            status_name = "UPGRADE_STARTED" if update['status'] == DeviceUpgradeStatus.UPGRADE_STARTED else "UPGRADE_COMPLETED"
            logger.info(f"Update {i+1}: Device {update['device_id']}, Status: {status_name}, Message: {update['message']}")
        
        # 验证结果
        success = True
        if len(status_updates) != 2:
            logger.error(f"Expected 2 status updates, got {len(status_updates)}")
            success = False
        else:
            # 检查第一个更新（升级开始）
            first_update = status_updates[0]
            if first_update['device_id'] != device_number:
                logger.error(f"First update device_id mismatch: expected {device_number}, got {first_update['device_id']}")
                success = False
            if first_update['status'] != DeviceUpgradeStatus.UPGRADE_STARTED:
                logger.error(f"First update status mismatch: expected {DeviceUpgradeStatus.UPGRADE_STARTED}, got {first_update['status']}")
                success = False
            
            # 检查第二个更新（升级结束）
            second_update = status_updates[1]
            if second_update['device_id'] != device_number:
                logger.error(f"Second update device_id mismatch: expected {device_number}, got {second_update['device_id']}")
                success = False
            if second_update['status'] != DeviceUpgradeStatus.UPGRADE_COMPLETED:
                logger.error(f"Second update status mismatch: expected {DeviceUpgradeStatus.UPGRADE_COMPLETED}, got {second_update['status']}")
                success = False
        
        if success:
            logger.info("✅ Device status update test PASSED!")
        else:
            logger.error("❌ Device status update test FAILED!")
        
        client_socket.close()
        return success
        
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        return False
    finally:
        server.stop()


if __name__ == '__main__':
    success = test_device_status_update()
    sys.exit(0 if success else 1)
